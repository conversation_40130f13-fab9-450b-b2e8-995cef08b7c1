import { getEnumsByLabel } from "@/api/base-config";
import Vue from "vue";

const enumsCache = new Map();

/**
 * Fetches and caches enums by their modelLabel.
 * @param {string} modelLabel - The label of the enum model to fetch.
 * @returns {Promise<Array>} A promise that resolves to an array of enum options.
 */
async function fetchEnum(modelLabel) {
  if (enumsCache.has(modelLabel)) {
    return enumsCache.get(modelLabel);
  }

  try {
    const response = await getEnumsByLabel({ modelLabel });
    if (response.code === 0 && Array.isArray(response.data)) {
      const options = response.data.map(item => ({
        value: item.id,
        label: item.text
      }));
      enumsCache.set(modelLabel, options);
      return options;
    } else {
      console.error(
        `Failed to fetch enum for modelLabel: ${modelLabel}`,
        response.msg || "Unknown error"
      );
      return [];
    }
  } catch (error) {
    console.error(`Error fetching enum for modelLabel: ${modelLabel}`, error);
    return [];
  }
}

export const enums = Vue.observable({
  vpp_site_type: [],
  vpp_resource_type: [],
  vpp_response_type: [],
  vpp_response_mode: [],
  vpp_resource_status: [],
  voltagelevel: [],
  powergenerationmode: [],
  vpp_type: [],
});

const enumMapping = {
  SITE_TYPE_CODES: "vpp_site_type",
  RESOURCE_STATUS_CODES: "vpp_resource_status",
  RESOURCE_TYPE: "vpp_resource_type",
  RESPONSE_MODE: "vpp_response_mode",
  VPP_DEVICE_TYPE: "vpp_device_type",
  VOLTAGE_LEVEL: "voltagelevel",
  POWER_GENERATION_MODE: "powergenerationmode",
  Network_Device_Type: "deviceclass",
  VPP_TYPE: "vpp_type",
};

/**
 * Loads all necessary enums into the reactive store.
 * Can be called once at application startup.
 */
export async function loadAllEnums() {
  const promises = Object.values(enumMapping).map(async modelLabel => {
    enums[modelLabel] = await fetchEnum(modelLabel);
  });
  await Promise.all(promises);
}

/**
 * Gets a specific enum list from the store.
 * @param {string} enumName - The original enum name (e.g., 'SITE_TYPE_CODES').
 * @returns {Array} The list of enum options.
 */
export function getEnumOptions(enumName) {
  const modelLabel = enumMapping[enumName];
  return modelLabel ? enums[modelLabel] : [];
}

/**
 * Gets a specific enum label by its value.
 * @param {string} enumName - The original enum name.
 * @param {string|number} value - The enum value.
 * @returns {string} The enum label.
 */
export function getEnumLabel(enumName, value) {
  const options = getEnumOptions(enumName);
  const option = options.find(opt => opt.value == value); // Use == for loose comparison
  return option ? option.label : String(value);
}
